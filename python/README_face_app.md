# InspireFace 人脸识别应用程序

基于InspireFace项目开发的完整人脸识别GUI应用程序，支持实时人脸检测、录入和识别功能。

## 功能特性

### 核心功能
- **实时视频显示**：显示摄像头的实时视频流
- **人脸检测**：自动检测视频中的人脸并绘制检测框
- **人脸录入**：录入新人脸并关联姓名
- **人脸识别**：识别已录入的人脸并显示姓名
- **数据持久化**：自动保存人脸特征和姓名映射

### 界面布局
- **左侧**：实时视频显示区域（带人脸检测框和识别结果标签）
- **右侧控制面板**：
  - 人脸录入区域（姓名输入框、录入按钮、自动生成姓名）
  - 已录入人员列表
  - 系统状态显示
- **底部**：控制按钮（开始/停止识别、退出程序）

## 安装要求

### 系统依赖
```bash
# 安装Python依赖
pip install opencv-python
pip install Pillow
pip install numpy

# InspireFace已包含在项目中
```

### 硬件要求
- 摄像头设备
- 支持OpenCV的操作系统

## 使用方法

### 启动应用
```bash
cd python
python face_recognition_app.py
```

### 基本操作流程

1. **启动应用**
   - 运行程序后会自动初始化InspireFace和摄像头
   - 界面显示"系统就绪"状态

2. **开始人脸识别**
   - 点击"开始识别"按钮
   - 摄像头开始捕获视频流
   - 自动检测人脸并显示检测框

3. **录入新人脸**
   - 确保视频中有清晰的人脸
   - 在"姓名"输入框中输入姓名，或点击"自动生成姓名"
   - 点击"录入当前人脸"按钮
   - 系统会提取人脸特征并保存到数据库

4. **人脸识别**
   - 已录入的人脸会自动被识别
   - 在检测框附近显示姓名和置信度
   - 置信度阈值为0.48（可在代码中调整）

5. **查看已录入人员**
   - 右侧列表显示所有已录入的人员
   - 点击"刷新列表"更新显示

## 技术架构

### 核心组件
- **GUI框架**：tkinter（Python标准库）
- **视频处理**：OpenCV
- **人脸识别**：InspireFace API
- **数据存储**：SQLite数据库 + JSON配置文件

### 数据存储
- `face_database.db`：SQLite数据库，存储人脸特征向量
- `name_mapping.json`：JSON文件，存储ID到姓名的映射关系

### 关键API使用
```python
# InspireFace会话创建
session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)

# 人脸检测
faces = session.face_detection(image)

# 特征提取
feature = session.face_feature_extract(image, face)

# FeatureHub配置和使用
config = isf.FeatureHubConfiguration(...)
isf.feature_hub_enable(config)
isf.feature_hub_face_insert(identity)
isf.feature_hub_face_search(feature)
```

## 配置选项

### 检测参数
- 检测置信度阈值：0.5
- 识别置信度阈值：0.48
- 最大检测人脸数：10

### 摄像头参数
- 分辨率：640x480
- 帧率：30fps
- 设备ID：0（默认摄像头）

## 故障排除

### 常见问题

1. **摄像头无法打开**
   - 检查摄像头是否被其他程序占用
   - 确认摄像头驱动正常
   - 尝试更改摄像头设备ID

2. **InspireFace初始化失败**
   - 确认InspireFace库正确安装
   - 检查模型文件是否存在
   - 查看错误日志信息

3. **人脸检测效果不佳**
   - 确保光线充足
   - 调整摄像头角度和距离
   - 降低检测置信度阈值

4. **识别准确率低**
   - 录入时确保人脸清晰
   - 多角度录入同一人的人脸
   - 调整识别置信度阈值

### 日志和调试
- 状态信息显示在界面右下角
- 详细错误信息会弹出对话框
- 可在代码中启用更详细的日志输出

## 扩展功能

### 可能的改进方向
1. **批量导入**：支持从图片文件批量录入人脸
2. **人脸管理**：支持删除、编辑已录入的人脸信息
3. **统计功能**：记录识别历史和统计信息
4. **多摄像头**：支持多个摄像头同时工作
5. **网络功能**：支持远程数据库和网络识别

### 性能优化
1. **多线程处理**：分离视频捕获和人脸处理线程
2. **缓存机制**：缓存常用的识别结果
3. **GPU加速**：利用GPU加速人脸检测和识别

## 许可证

本应用基于InspireFace项目开发，遵循相应的开源许可证。

## 联系方式

如有问题或建议，请参考InspireFace项目的官方文档和社区支持。
