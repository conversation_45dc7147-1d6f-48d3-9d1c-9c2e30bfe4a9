#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人脸识别应用程序
基于InspireFace实现的实时人脸检测、录入和识别系统

功能特性：
- 实时视频显示
- 人脸检测和绘制检测框
- 人脸录入功能
- 人脸识别功能
- 持久化存储
"""

import os
import cv2
import json
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import time
from PIL import Image, ImageTk
import numpy as np
import inspireface as isf
from typing import Dict, List, Optional, Tuple
import random
import string

class FaceRecognitionApp:
    def __init__(self):
        """初始化人脸识别应用程序"""
        self.root = tk.Tk()
        self.root.title("InspireFace 人脸识别系统")
        self.root.geometry("1200x800")
        
        # 应用状态
        self.is_running = False
        self.current_frame = None
        self.detected_faces = []
        
        # 数据存储
        self.face_database_path = "face_database.db"
        self.name_mapping_path = "name_mapping.json"
        self.name_mapping = self.load_name_mapping()
        
        # InspireFace相关
        self.session = None
        self.feature_hub_enabled = False
        
        # GUI组件
        self.video_label = None
        self.status_label = None
        self.name_entry = None
        self.face_list = None
        
        # 初始化
        self.setup_inspireface()
        self.setup_gui()
        self.setup_camera()
        
    def setup_inspireface(self):
        """初始化InspireFace会话和FeatureHub"""
        try:
            # 启动InspireFace
            if not isf.query_launch_status():
                ret = isf.launch("Pikachu")
                if not ret:
                    raise Exception("InspireFace启动失败")
            
            # 创建会话，启用人脸识别功能
            opt = (isf.HF_ENABLE_FACE_RECOGNITION | 
                   isf.HF_ENABLE_QUALITY | 
                   isf.HF_ENABLE_MASK_DETECT)
            
            self.session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)
            self.session.set_detection_confidence_threshold(0.5)
            
            # 配置FeatureHub
            feature_hub_config = isf.FeatureHubConfiguration(
                primary_key_mode=isf.HF_PK_AUTO_INCREMENT,
                enable_persistence=True,
                persistence_db_path=self.face_database_path,
                search_threshold=0.48,
                search_mode=isf.HF_SEARCH_MODE_EAGER,
            )
            
            ret = isf.feature_hub_enable(feature_hub_config)
            if not ret:
                raise Exception("FeatureHub启用失败")
            
            self.feature_hub_enabled = True
            print(f"InspireFace初始化成功，已录入人脸数量: {isf.feature_hub_get_face_count()}")
            
        except Exception as e:
            messagebox.showerror("初始化错误", f"InspireFace初始化失败: {str(e)}")
            self.root.quit()
    
    def setup_camera(self):
        """初始化摄像头"""
        try:
            self.cap = cv2.VideoCapture(1)
            if not self.cap.isOpened():
                raise Exception("无法打开摄像头")
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
        except Exception as e:
            messagebox.showerror("摄像头错误", f"摄像头初始化失败: {str(e)}")
            self.root.quit()
    
    def setup_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=2)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        
        # 左侧：视频显示区域
        video_frame = ttk.LabelFrame(main_frame, text="实时视频", padding="5")
        video_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        self.video_label = tk.Label(video_frame, bg="black", width=80, height=30)
        self.video_label.pack(expand=True, fill=tk.BOTH)
        
        # 右侧：控制面板
        control_frame = ttk.LabelFrame(main_frame, text="控制面板", padding="5")
        control_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 录入人脸区域
        register_frame = ttk.LabelFrame(control_frame, text="人脸录入", padding="5")
        register_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(register_frame, text="姓名:").pack(anchor=tk.W)
        self.name_entry = ttk.Entry(register_frame, width=20)
        self.name_entry.pack(fill=tk.X, pady=(0, 5))
        
        register_btn = ttk.Button(register_frame, text="录入当前人脸", 
                                command=self.register_face)
        register_btn.pack(fill=tk.X, pady=(0, 5))
        
        auto_name_btn = ttk.Button(register_frame, text="自动生成姓名", 
                                 command=self.generate_random_name)
        auto_name_btn.pack(fill=tk.X)
        
        # 已录入人员列表
        list_frame = ttk.LabelFrame(control_frame, text="已录入人员", padding="5")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建列表框和滚动条
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        self.face_list = tk.Listbox(list_container, yscrollcommand=scrollbar.set)
        self.face_list.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.face_list.yview)
        
        # 刷新列表按钮
        refresh_btn = ttk.Button(list_frame, text="刷新列表", 
                               command=self.refresh_face_list)
        refresh_btn.pack(fill=tk.X, pady=(5, 0))
        
        # 状态显示区域
        status_frame = ttk.LabelFrame(control_frame, text="系统状态", padding="5")
        status_frame.pack(fill=tk.X)
        
        self.status_label = tk.Label(status_frame, text="系统就绪", 
                                   fg="green", wraplength=200, justify=tk.LEFT)
        self.status_label.pack(anchor=tk.W)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        self.start_btn = ttk.Button(button_frame, text="开始识别", 
                                  command=self.start_recognition)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_btn = ttk.Button(button_frame, text="停止识别", 
                                 command=self.stop_recognition, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        exit_btn = ttk.Button(button_frame, text="退出程序", 
                            command=self.on_closing)
        exit_btn.pack(side=tk.RIGHT)
        
        # 初始化人员列表
        self.refresh_face_list()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def load_name_mapping(self) -> Dict[int, str]:
        """加载姓名映射"""
        if os.path.exists(self.name_mapping_path):
            try:
                with open(self.name_mapping_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_name_mapping(self):
        """保存姓名映射"""
        try:
            with open(self.name_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self.name_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存姓名映射失败: {e}")
    
    def generate_random_name(self):
        """生成随机姓名"""
        surnames = ["张", "李", "王", "刘", "陈", "杨", "赵", "黄", "周", "吴"]
        given_names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋"]
        
        surname = random.choice(surnames)
        given_name = random.choice(given_names)
        random_name = f"{surname}{given_name}"
        
        self.name_entry.delete(0, tk.END)
        self.name_entry.insert(0, random_name)

    def start_recognition(self):
        """开始人脸识别"""
        if not self.is_running:
            self.is_running = True
            self.start_btn.config(state=tk.DISABLED)
            self.stop_btn.config(state=tk.NORMAL)
            self.update_status("开始人脸识别...")
            self.update_video()

    def stop_recognition(self):
        """停止人脸识别"""
        self.is_running = False
        self.start_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.DISABLED)
        self.update_status("人脸识别已停止")

    def update_video(self):
        """更新视频帧"""
        if not self.is_running:
            return

        try:
            ret, frame = self.cap.read()
            if ret:
                self.current_frame = frame.copy()

                # 人脸检测和识别
                self.detect_and_recognize(frame)

                # 转换为tkinter可显示的格式
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                frame_pil = Image.fromarray(frame_rgb)
                frame_tk = ImageTk.PhotoImage(frame_pil)

                # 更新显示
                self.video_label.configure(image=frame_tk)
                self.video_label.image = frame_tk

        except Exception as e:
            self.update_status(f"视频更新错误: {str(e)}")

        # 继续更新
        if self.is_running:
            self.root.after(30, self.update_video)  # 约33fps

    def detect_and_recognize(self, frame):
        """检测和识别人脸"""
        try:
            # 人脸检测
            faces = self.session.face_detection(frame)
            self.detected_faces = faces

            if faces:
                self.update_status(f"检测到 {len(faces)} 张人脸")

                for face in faces:
                    # 绘制检测框
                    self.draw_face_box(frame, face)

                    # 人脸识别
                    if self.feature_hub_enabled:
                        self.recognize_face(frame, face)
            else:
                self.update_status("未检测到人脸")

        except Exception as e:
            self.update_status(f"人脸检测错误: {str(e)}")

    def draw_face_box(self, frame, face):
        """绘制人脸检测框"""
        x1, y1, x2, y2 = face.location

        # 计算旋转矩形
        center = ((x1 + x2) / 2, (y1 + y2) / 2)
        size = (x2 - x1, y2 - y1)
        angle = face.roll

        rect = ((center[0], center[1]), (size[0], size[1]), angle)
        box = cv2.boxPoints(rect).astype(int)

        # 绘制旋转的检测框
        cv2.drawContours(frame, [box], 0, (0, 255, 0), 2)

        # 绘制置信度
        confidence_text = f"{face.detection_confidence:.2f}"
        cv2.putText(frame, confidence_text, (int(x1), int(y1) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    def recognize_face(self, frame, face):
        """识别人脸"""
        try:
            # 提取特征
            feature = self.session.face_feature_extract(frame, face)

            # 搜索相似人脸
            search_result = isf.feature_hub_face_search(feature)

            if search_result.similar_identity.id != -1 and search_result.confidence > 0.48:
                # 找到匹配的人脸
                face_id = search_result.similar_identity.id
                name = self.name_mapping.get(str(face_id), f"ID_{face_id}")
                confidence = search_result.confidence

                # 在人脸上显示姓名和置信度
                x1, y1, x2, y2 = face.location
                label = f"{name} ({confidence:.2f})"

                # 计算文本位置
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                text_x = int(x1)
                text_y = int(y2) + 25

                # 绘制背景矩形
                cv2.rectangle(frame, (text_x, text_y - text_size[1] - 5),
                            (text_x + text_size[0], text_y + 5), (0, 255, 0), -1)

                # 绘制文本
                cv2.putText(frame, label, (text_x, text_y),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)

        except Exception as e:
            print(f"人脸识别错误: {e}")

    def register_face(self):
        """录入人脸"""
        if not self.detected_faces or self.current_frame is None:
            messagebox.showwarning("警告", "请先检测到人脸再进行录入")
            return

        name = self.name_entry.get().strip()
        if not name:
            messagebox.showwarning("警告", "请输入姓名")
            return

        try:
            # 使用第一个检测到的人脸
            face = self.detected_faces[0]

            # 提取特征
            feature = self.session.face_feature_extract(self.current_frame, face)

            # 创建人脸身份
            identity = isf.FaceIdentity(feature, id=-1)  # 使用自动分配ID

            # 插入到FeatureHub
            ret, alloc_id = isf.feature_hub_face_insert(identity)

            if ret:
                # 保存姓名映射
                self.name_mapping[str(alloc_id)] = name
                self.save_name_mapping()

                # 清空输入框
                self.name_entry.delete(0, tk.END)

                # 刷新列表
                self.refresh_face_list()

                self.update_status(f"成功录入人脸: {name} (ID: {alloc_id})")
                messagebox.showinfo("成功", f"人脸录入成功!\n姓名: {name}\nID: {alloc_id}")

            else:
                messagebox.showerror("错误", "人脸录入失败")

        except Exception as e:
            messagebox.showerror("错误", f"录入过程中发生错误: {str(e)}")

    def refresh_face_list(self):
        """刷新已录入人员列表"""
        try:
            self.face_list.delete(0, tk.END)

            if self.feature_hub_enabled:
                face_count = isf.feature_hub_get_face_count()

                # 显示所有已录入的人脸
                for face_id_str, name in self.name_mapping.items():
                    self.face_list.insert(tk.END, f"{name} (ID: {face_id_str})")

                # 如果有未命名的人脸，也显示出来
                for i in range(face_count):
                    if str(i) not in self.name_mapping:
                        self.face_list.insert(tk.END, f"未命名 (ID: {i})")

        except Exception as e:
            print(f"刷新列表错误: {e}")

    def update_status(self, message: str):
        """更新状态显示"""
        self.status_label.config(text=message)
        self.root.update_idletasks()

    def on_closing(self):
        """程序关闭处理"""
        self.is_running = False

        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()

        # 保存数据
        self.save_name_mapping()

        # 关闭InspireFace
        if self.session:
            self.session.release()

        if self.feature_hub_enabled:
            isf.feature_hub_disable()

        self.root.quit()
        self.root.destroy()

    def run(self):
        """运行应用程序"""
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = FaceRecognitionApp()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        messagebox.showerror("启动错误", f"应用程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
