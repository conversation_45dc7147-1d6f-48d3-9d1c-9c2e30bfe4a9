#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InspireFace 人脸识别命令行版本
真正的CLI交互界面，支持实际的人脸检测和识别功能

功能特性：
- 实时视频显示
- 真实的人脸检测和识别
- 命令行交互界面
- 英文姓名支持
- 多线程架构
- 持久化存储
"""

import os
import cv2
import json
import numpy as np
import inspireface as isf
from typing import Dict, List, Optional, Tuple
import random
import time
import threading
import queue

class FaceRecognitionCLI:
    def __init__(self):
        """初始化人脸识别应用程序"""
        print("=" * 60)
        print("InspireFace 人脸识别系统 - CLI版本")
        print("=" * 60)
        print("正在初始化系统...")

        # 应用状态
        self.is_running = False
        self.video_running = False
        self.current_frame = None
        self.detected_faces = []

        # 线程控制
        self.command_queue = queue.Queue()
        self.video_thread = None
        self.command_thread = None
        self.frame_lock = threading.Lock()  # 添加帧锁保护

        # 数据存储
        self.face_database_path = "face_database.db"
        self.name_mapping_path = "name_mapping.json"
        self.name_mapping = self.load_name_mapping()

        # InspireFace相关
        self.session = None
        self.feature_hub_enabled = False

        # 初始化组件
        try:
            self.setup_inspireface()
            self.setup_camera()
            print("✅ 系统初始化完成！")
            self.print_instructions()
        except Exception as e:
            print(f"❌ 系统初始化失败: {e}")
            raise
        
    def setup_inspireface(self):
        """初始化InspireFace会话和FeatureHub"""
        try:
            print("正在启动InspireFace引擎...")

            # 检查并启动InspireFace
            if not isf.query_launch_status():
                print("启动InspireFace核心引擎...")
                ret = isf.launch("Pikachu")
                if not ret:
                    raise Exception("InspireFace核心引擎启动失败")
                print("✅ InspireFace核心引擎启动成功")
            else:
                print("✅ InspireFace核心引擎已运行")

            # 创建会话，启用人脸识别功能
            print("创建InspireFace会话...")
            opt = (isf.HF_ENABLE_FACE_RECOGNITION |
                   isf.HF_ENABLE_QUALITY |
                   isf.HF_ENABLE_MASK_DETECT)

            self.session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)
            self.session.set_detection_confidence_threshold(0.5)
            print("✅ InspireFace会话创建成功")

            # 配置FeatureHub数据库
            print("配置FeatureHub数据库...")
            feature_hub_config = isf.FeatureHubConfiguration(
                primary_key_mode=isf.HF_PK_AUTO_INCREMENT,
                enable_persistence=True,
                persistence_db_path=self.face_database_path,
                search_threshold=0.48,
                search_mode=isf.HF_SEARCH_MODE_EAGER,
            )

            ret = isf.feature_hub_enable(feature_hub_config)
            if not ret:
                raise Exception("FeatureHub数据库启用失败")

            self.feature_hub_enabled = True
            face_count = isf.feature_hub_get_face_count()
            print(f"✅ FeatureHub数据库启用成功，已录入人脸数量: {face_count}")

        except Exception as e:
            print(f"❌ InspireFace初始化失败: {str(e)}")
            print("请检查InspireFace是否正确安装和配置")
            raise
    
    def setup_camera(self):
        """初始化摄像头"""
        try:
            print("正在初始化摄像头...")

            # 尝试摄像头ID 1
            print("尝试打开摄像头ID 1...")
            self.cap = cv2.VideoCapture(1)
            if not self.cap.isOpened():
                print("⚠️  摄像头ID 1 不可用，尝试ID 0...")
                self.cap.release()

                # 尝试摄像头ID 0
                print("尝试打开摄像头ID 0...")
                self.cap = cv2.VideoCapture(0)
                if not self.cap.isOpened():
                    raise Exception("无法打开摄像头 (尝试了ID 0和1)")
                else:
                    print("✅ 成功使用摄像头ID 0")
            else:
                print("✅ 成功使用摄像头ID 1")

            # 设置摄像头参数
            print("配置摄像头参数...")
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)

            # 测试读取一帧
            print("测试摄像头读取...")
            ret, _ = self.cap.read()
            if not ret:
                raise Exception("摄像头无法读取图像")

            print("✅ 摄像头初始化成功")

        except Exception as e:
            print(f"❌ 摄像头初始化失败: {str(e)}")
            print("请检查摄像头是否正确连接并且没有被其他程序占用")
            raise
    
    def load_name_mapping(self) -> Dict[int, str]:
        """加载姓名映射"""
        if os.path.exists(self.name_mapping_path):
            try:
                with open(self.name_mapping_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_name_mapping(self):
        """保存姓名映射"""
        try:
            with open(self.name_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self.name_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存姓名映射失败: {e}")
    
    def generate_random_name(self) -> str:
        """生成随机英文姓名"""
        first_names = ["James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
                      "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
                      "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa"]

        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
                     "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas"]

        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        return f"{first_name} {last_name}"
    
    def print_instructions(self):
        """打印操作说明"""
        print("\n" + "=" * 60)
        print("📖 操作说明:")
        print("1. 程序启动后会显示摄像头视频窗口（仅用于查看）")
        print("2. 所有操作通过在此终端中输入命令完成")
        print("3. 可用命令:")
        print("   • register  → 录入当前检测到的人脸")
        print("   • list      → 列出所有已录入的人员")
        print("   • status    → 显示系统状态")
        print("   • help      → 显示帮助信息")
        print("   • quit      → 退出程序")
        print("4. 在命令提示符后输入命令并按回车执行")
        print("5. 视频窗口会实时显示人脸检测和识别结果")
        print("6. 系统会自动生成英文姓名")
        print("=" * 60)
        print("💡 提示: 视频窗口仅用于查看，所有操作在此终端进行！")
        print("=" * 60 + "\n")
    
    def detect_and_recognize(self, frame):
        """检测和识别人脸 - 简化版本，避免重复处理"""
        # 这个方法现在主要在视频线程中直接处理，这里保留作为备用
        pass

    def draw_face_box(self, frame, face):
        """绘制人脸检测框"""
        x1, y1, x2, y2 = face.location

        # 绘制简单矩形框
        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

        # 绘制置信度
        confidence_text = f"置信度: {face.detection_confidence:.2f}"
        cv2.putText(frame, confidence_text, (int(x1), int(y1) - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    def recognize_face(self, frame, face):
        """识别人脸"""
        try:
            # 提取特征
            feature = self.session.face_feature_extract(frame, face)
            
            # 搜索相似人脸
            search_result = isf.feature_hub_face_search(feature)
            
            if search_result.similar_identity.id != -1 and search_result.confidence > 0.48:
                # 找到匹配的人脸
                face_id = search_result.similar_identity.id
                name = self.name_mapping.get(str(face_id), f"ID_{face_id}")
                confidence = search_result.confidence
                
                # 在人脸上显示姓名和置信度
                x1, y1, x2, y2 = face.location
                label = f"{name} ({confidence:.2f})"
                
                # 计算文本位置
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                text_x = int(x1)
                text_y = int(y2) + 25
                
                # 绘制背景矩形
                cv2.rectangle(frame, (text_x, text_y - text_size[1] - 5), 
                            (text_x + text_size[0], text_y + 5), (0, 255, 0), -1)
                
                # 绘制文本
                cv2.putText(frame, label, (text_x, text_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
        except Exception as e:
            print(f"人脸识别错误: {e}")
    
    def register_face(self):
        """录入人脸"""
        # 使用线程锁保护数据访问
        with self.frame_lock:
            if not self.detected_faces or self.current_frame is None:
                print("⚠️  当前未检测到人脸，请等待检测到人脸后再试")
                return

            # 复制当前帧和人脸数据
            current_frame = self.current_frame.copy()
            current_faces = self.detected_faces.copy()

        # 生成随机英文姓名
        name = self.generate_random_name()
        print(f"🔄 正在录入人脸，姓名: {name}")

        try:
            # 使用第一个检测到的人脸
            face = current_faces[0]

            # 提取特征
            print("📊 正在提取人脸特征...")
            feature = self.session.face_feature_extract(current_frame, face)

            # 创建人脸身份
            identity = isf.FaceIdentity(feature, id=-1)  # 使用自动分配ID

            # 插入到FeatureHub
            print("💾 正在保存到数据库...")
            ret, alloc_id = isf.feature_hub_face_insert(identity)

            if ret:
                # 保存姓名映射
                self.name_mapping[str(alloc_id)] = name
                self.save_name_mapping()

                print(f"✅ 成功录入人脸: {name} (ID: {alloc_id})")
                print(f"📊 当前数据库中共有 {len(self.name_mapping)} 个已录入人脸")

            else:
                print("❌ 人脸录入失败，请重试")

        except Exception as e:
            print(f"❌ 录入过程中发生错误: {str(e)}")
            print("请确保人脸清晰可见并重试")
    
    def list_registered_faces(self):
        """列出已录入的人员"""
        print("\n" + "=" * 40)
        print("👥 已录入人员列表:")
        print("-" * 40)

        if not self.name_mapping:
            print("暂无已录入人员")
        else:
            for i, (face_id, name) in enumerate(self.name_mapping.items(), 1):
                print(f"{i:2d}. ID: {face_id:3s} - 姓名: {name}")

        if self.feature_hub_enabled:
            total_count = isf.feature_hub_get_face_count()
            print(f"\n📊 统计: {len(self.name_mapping)} 个已命名, {total_count} 个总数")

        print("=" * 40 + "\n")

    def show_status(self):
        """显示系统状态"""
        print("\n" + "=" * 40)
        print("📊 系统状态:")
        print("-" * 40)
        print(f"InspireFace状态: {'✅ 正常' if self.session else '❌ 异常'}")
        print(f"FeatureHub状态: {'✅ 启用' if self.feature_hub_enabled else '❌ 禁用'}")
        print(f"摄像头状态: {'✅ 正常' if self.cap.isOpened() else '❌ 异常'}")
        print(f"当前检测到人脸数: {len(self.detected_faces)}")

        if self.feature_hub_enabled:
            print(f"数据库中人脸总数: {isf.feature_hub_get_face_count()}")

        print("=" * 40 + "\n")

    def show_help(self):
        """显示帮助信息"""
        print("\n" + "=" * 60)
        print("🆘 帮助信息:")
        print("-" * 60)
        print("可用命令 (在此终端中输入):")
        print("  register  - 录入当前检测到的人脸")
        print("  list      - 列出所有已录入的人员")
        print("  status    - 显示系统状态")
        print("  help      - 显示此帮助信息")
        print("  quit      - 退出程序")
        print("\n简写命令:")
        print("  r         - register 的简写")
        print("  l         - list 的简写")
        print("  s         - status 的简写")
        print("  h         - help 的简写")
        print("  q         - quit 的简写")
        print("\n💡 使用技巧:")
        print("  • 视频窗口仅用于查看，所有操作在此终端进行")
        print("  • 录入时保持人脸清晰且正对摄像头")
        print("  • 光线充足时识别效果更好")
        print("  • 系统会自动生成英文姓名")
        print("  • 可以按ESC键退出程序")
        print("=" * 60 + "\n")

    def video_processing_thread(self):
        """视频处理线程 - 负责视频显示和人脸检测识别"""
        print("🎥 视频处理线程启动...")

        # 检查必要组件
        if not hasattr(self, 'cap') or not self.cap.isOpened():
            print("❌ 摄像头不可用，视频线程退出")
            return

        if not self.session:
            print("❌ InspireFace会话不可用，视频线程退出")
            return

        # 创建窗口
        window_name = 'InspireFace 人脸识别系统 - CLI模式'
        cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
        print(f"📺 创建视频窗口: {window_name}")

        frame_count = 0
        error_count = 0
        max_errors = 5  # 减少最大错误次数

        while self.video_running and self.is_running:
            try:
                ret, frame = self.cap.read()
                if not ret:
                    print("❌ 无法读取摄像头画面")
                    time.sleep(0.1)
                    continue

                # 使用线程锁保护帧数据
                with self.frame_lock:
                    self.current_frame = frame.copy()

                # 人脸检测和识别
                try:
                    faces = self.session.face_detection(frame)
                    self.detected_faces = faces

                    # 绘制检测框和识别结果
                    for face in faces:
                        x1, y1, x2, y2 = face.location

                        # 绘制检测框
                        cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                        # 显示置信度
                        confidence_text = f"置信度: {face.detection_confidence:.2f}"
                        cv2.putText(frame, confidence_text, (int(x1), int(y1) - 10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                        # 人脸识别
                        if self.feature_hub_enabled:
                            try:
                                feature = self.session.face_feature_extract(frame, face)
                                search_result = isf.feature_hub_face_search(feature)

                                if search_result.similar_identity.id != -1 and search_result.confidence > 0.48:
                                    face_id = search_result.similar_identity.id
                                    name = self.name_mapping.get(str(face_id), f"未知_{face_id}")
                                    confidence = search_result.confidence

                                    # 显示识别结果
                                    label = f"{name} ({confidence:.2f})"
                                    text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                                    text_x = int(x1)
                                    text_y = int(y2) + 25

                                    # 绘制背景
                                    cv2.rectangle(frame, (text_x, text_y - text_size[1] - 5),
                                                (text_x + text_size[0], text_y + 5), (0, 255, 0), -1)

                                    # 绘制文本
                                    cv2.putText(frame, label, (text_x, text_y),
                                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                            except Exception as e:
                                # 识别失败时静默处理
                                pass

                    error_count = 0  # 重置错误计数

                except Exception as e:
                    error_count += 1
                    if error_count <= 2:  # 只显示前2次错误
                        print(f"❌ 人脸处理错误: {e}")
                    if error_count >= max_errors:
                        self.detected_faces = []

                # 在画面上显示状态信息
                cv2.putText(frame, "InspireFace CLI - 请在终端输入命令",
                           (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(frame, f"检测到人脸: {len(self.detected_faces)}",
                           (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(frame, f"已录入: {len(self.name_mapping)} 人",
                           (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                # 显示帧数
                frame_count += 1
                if frame_count == 1:
                    print("📺 视频窗口已显示，请查看视频窗口")
                elif frame_count % 60 == 0:  # 每60帧显示一次状态
                    print(f"📹 视频正常运行，已处理 {frame_count} 帧，检测到 {len(self.detected_faces)} 张人脸")

                # 显示画面
                cv2.imshow(window_name, frame)

                # 检查按键
                key = cv2.waitKey(1) & 0xFF
                if key == 27:  # ESC键
                    print("🔔 检测到ESC键，程序将退出...")
                    self.is_running = False
                    break
                elif key == ord('q'):  # Q键
                    print("🔔 检测到Q键，程序将退出...")
                    self.is_running = False
                    break

                # 控制帧率
                time.sleep(0.033)  # 约30fps

            except Exception as e:
                error_count += 1
                if error_count <= 2:
                    print(f"❌ 视频处理错误: {e}")
                time.sleep(0.1)
                continue

        # 清理窗口
        cv2.destroyWindow(window_name)
        print("🎥 视频处理线程结束")

    def command_input_thread(self):
        """命令输入处理线程"""
        print("⌨️  命令输入线程启动...")
        print("📝 请输入命令 (输入 'help' 查看可用命令):")

        while self.is_running:
            try:
                command = input(">>> ").strip().lower()
                if command:
                    self.command_queue.put(command)

                    # 如果是退出命令，立即停止
                    if command in ['quit', 'exit', 'q']:
                        break

            except (EOFError, KeyboardInterrupt):
                print("\n🔔 检测到中断信号，程序将退出...")
                self.command_queue.put('quit')
                break
            except Exception as e:
                print(f"❌ 输入错误: {e}")

        print("⌨️  命令输入线程结束")

    def process_commands(self):
        """处理命令队列中的命令"""
        try:
            while not self.command_queue.empty():
                command = self.command_queue.get_nowait()

                if command in ['quit', 'exit', 'q']:
                    print("👋 正在退出程序...")
                    self.is_running = False
                    self.video_running = False
                elif command in ['register', 'r']:
                    self.register_face()
                elif command in ['list', 'l']:
                    self.list_registered_faces()
                elif command in ['status', 's']:
                    self.show_status()
                elif command in ['help', 'h']:
                    self.show_help()
                elif command == '':
                    pass  # 忽略空命令
                else:
                    print(f"❓ 未知命令: '{command}'，输入 'help' 查看可用命令")

        except queue.Empty:
            pass
        except Exception as e:
            print(f"❌ 命令处理错误: {e}")

    def run(self):
        """运行应用程序 - 主线程处理视频，子线程处理命令"""
        print("🚀 启动人脸识别系统...")
        print("📺 正在初始化视频窗口...")
        print("⌨️  命令输入将在后台线程运行")

        self.is_running = True
        self.video_running = True

        try:
            # 在主线程中创建OpenCV窗口
            window_name = 'InspireFace 人脸识别系统 - CLI模式'
            cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
            print(f"📺 创建视频窗口: {window_name}")

            # 启动命令输入线程
            print("⌨️  正在启动命令输入线程...")
            self.command_thread = threading.Thread(target=self.command_input_thread, daemon=True)
            self.command_thread.start()

            print("✅ 系统启动完成！")
            print("💡 视频窗口已显示，请在终端中输入命令")
            print("� 可用命令: register, list, status, help, quit")

            frame_count = 0
            error_count = 0

            # 主线程处理视频显示
            while self.is_running:
                try:
                    # 处理命令队列
                    self.process_commands()

                    # 读取摄像头
                    ret, frame = self.cap.read()
                    if not ret:
                        print("❌ 无法读取摄像头画面")
                        time.sleep(0.1)
                        continue

                    # 保存当前帧
                    with self.frame_lock:
                        self.current_frame = frame.copy()

                    # 人脸检测和识别
                    try:
                        faces = self.session.face_detection(frame)
                        self.detected_faces = faces

                        # 绘制检测框和识别结果
                        for face in faces:
                            x1, y1, x2, y2 = face.location

                            # 绘制检测框
                            cv2.rectangle(frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                            # 显示置信度
                            confidence_text = f"置信度: {face.detection_confidence:.2f}"
                            cv2.putText(frame, confidence_text, (int(x1), int(y1) - 10),
                                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

                            # 人脸识别
                            if self.feature_hub_enabled:
                                try:
                                    feature = self.session.face_feature_extract(frame, face)
                                    search_result = isf.feature_hub_face_search(feature)

                                    if search_result.similar_identity.id != -1 and search_result.confidence > 0.48:
                                        face_id = search_result.similar_identity.id
                                        name = self.name_mapping.get(str(face_id), f"未知_{face_id}")
                                        confidence = search_result.confidence

                                        # 显示识别结果
                                        label = f"{name} ({confidence:.2f})"
                                        text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                                        text_x = int(x1)
                                        text_y = int(y2) + 25

                                        # 绘制背景
                                        cv2.rectangle(frame, (text_x, text_y - text_size[1] - 5),
                                                    (text_x + text_size[0], text_y + 5), (0, 255, 0), -1)

                                        # 绘制文本
                                        cv2.putText(frame, label, (text_x, text_y),
                                                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                                except:
                                    pass  # 识别失败时忽略

                        error_count = 0  # 重置错误计数

                    except Exception as e:
                        error_count += 1
                        if error_count <= 2:
                            print(f"❌ 人脸处理错误: {e}")

                    # 在画面上显示状态信息
                    cv2.putText(frame, "InspireFace CLI - 请在终端输入命令",
                               (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    cv2.putText(frame, f"检测到人脸: {len(self.detected_faces)}",
                               (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                    cv2.putText(frame, f"已录入: {len(self.name_mapping)} 人",
                               (10, 90), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

                    # 显示画面
                    cv2.imshow(window_name, frame)

                    # 检查按键
                    key = cv2.waitKey(1) & 0xFF
                    if key == 27 or key == ord('q'):  # ESC或Q键
                        print("🔔 检测到退出键，程序将退出...")
                        self.is_running = False
                        break

                    frame_count += 1
                    if frame_count == 1:
                        print("📺 视频窗口正常显示")
                    elif frame_count % 60 == 0:
                        print(f"📹 视频正常运行，已处理 {frame_count} 帧")

                    # 控制帧率
                    time.sleep(0.033)  # 约30fps

                except Exception as e:
                    error_count += 1
                    if error_count <= 2:
                        print(f"❌ 主循环错误: {e}")
                    time.sleep(0.1)

        except KeyboardInterrupt:
            print("\n👋 用户中断程序")
            self.is_running = False

        finally:
            cv2.destroyAllWindows()
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("🔄 正在清理资源...")

        # 停止所有线程
        self.is_running = False
        self.video_running = False

        # 等待线程结束
        if hasattr(self, 'video_thread') and self.video_thread and self.video_thread.is_alive():
            print("⏳ 等待视频线程结束...")
            self.video_thread.join(timeout=2)

        if hasattr(self, 'command_thread') and self.command_thread and self.command_thread.is_alive():
            print("⏳ 等待命令线程结束...")
            self.command_thread.join(timeout=2)

        # 释放摄像头资源
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()

        # 关闭OpenCV窗口
        cv2.destroyAllWindows()

        # 保存数据
        self.save_name_mapping()

        # 关闭InspireFace
        if self.session:
            self.session.release()

        if self.feature_hub_enabled:
            isf.feature_hub_disable()

        print("✅ 资源清理完成")
        print("👋 感谢使用InspireFace人脸识别系统！")


def main():
    """主函数"""
    try:
        app = FaceRecognitionCLI()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
