#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InspireFace 人脸识别命令行版本
不依赖GUI，使用OpenCV窗口显示

功能特性：
- 实时视频显示
- 人脸检测和绘制检测框
- 人脸录入功能（按键触发）
- 人脸识别功能
- 持久化存储
"""

import os
import cv2
import json
import numpy as np
import inspireface as isf
from typing import Dict, List, Optional, Tuple
import random
import string
import time
import threading
import queue

class FaceRecognitionCLI:
    def __init__(self):
        """初始化人脸识别应用程序"""
        print("初始化InspireFace人脸识别系统...")

        # 应用状态
        self.is_running = False
        self.current_frame = None
        self.detected_faces = []

        # 命令队列用于线程间通信
        self.command_queue = queue.Queue()

        # 数据存储
        self.face_database_path = "face_database.db"
        self.name_mapping_path = "name_mapping.json"
        self.name_mapping = self.load_name_mapping()

        # InspireFace相关
        self.session = None
        self.feature_hub_enabled = False

        # 初始化
        self.setup_inspireface()
        self.setup_camera()

        print("系统初始化完成！")
        self.print_instructions()
        
    def setup_inspireface(self):
        """初始化InspireFace会话和FeatureHub"""
        try:
            # 启动InspireFace
            if not isf.query_launch_status():
                ret = isf.launch("Pikachu")
                if not ret:
                    raise Exception("InspireFace启动失败")
            
            # 创建会话，启用人脸识别功能
            opt = (isf.HF_ENABLE_FACE_RECOGNITION | 
                   isf.HF_ENABLE_QUALITY | 
                   isf.HF_ENABLE_MASK_DETECT)
            
            self.session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)
            self.session.set_detection_confidence_threshold(0.5)
            
            # 配置FeatureHub
            feature_hub_config = isf.FeatureHubConfiguration(
                primary_key_mode=isf.HF_PK_AUTO_INCREMENT,
                enable_persistence=True,
                persistence_db_path=self.face_database_path,
                search_threshold=0.48,
                search_mode=isf.HF_SEARCH_MODE_EAGER,
            )
            
            ret = isf.feature_hub_enable(feature_hub_config)
            if not ret:
                raise Exception("FeatureHub启用失败")
            
            self.feature_hub_enabled = True
            print(f"InspireFace初始化成功，已录入人脸数量: {isf.feature_hub_get_face_count()}")
            
        except Exception as e:
            print(f"InspireFace初始化失败: {str(e)}")
            exit(1)
    
    def setup_camera(self):
        """初始化摄像头"""
        try:
            self.cap = cv2.VideoCapture(1)
            if not self.cap.isOpened():
                raise Exception("无法打开摄像头")
            
            # 设置摄像头参数
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, 30)
            
            print("摄像头初始化成功")
            
        except Exception as e:
            print(f"摄像头初始化失败: {str(e)}")
            exit(1)
    
    def load_name_mapping(self) -> Dict[int, str]:
        """加载姓名映射"""
        if os.path.exists(self.name_mapping_path):
            try:
                with open(self.name_mapping_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_name_mapping(self):
        """保存姓名映射"""
        try:
            with open(self.name_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self.name_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存姓名映射失败: {e}")
    
    def generate_random_name(self) -> str:
        """生成随机英文姓名"""
        first_names = ["James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda",
                      "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
                      "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa"]

        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
                     "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas"]

        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        return f"{first_name} {last_name}"
    
    def print_instructions(self):
        """打印操作说明"""
        print("\n" + "="*60)
        print("操作说明:")
        print("- 在OpenCV窗口中按键（确保窗口获得焦点）:")
        print("  * 按 'r' 键: 录入当前检测到的人脸")
        print("  * 按 'l' 键: 列出所有已录入的人员")
        print("  * 按 's' 键: 显示系统状态")
        print("  * 按 'q' 键: 退出程序")
        print("- 或者在命令行中输入命令:")
        print("  * 输入 'help' 查看帮助")
        print("  * 输入 'register' 录入人脸")
        print("  * 输入 'list' 查看人员列表")
        print("  * 输入 'status' 查看状态")
        print("  * 输入 'quit' 退出程序")
        print("="*60 + "\n")
    
    def detect_and_recognize(self, frame):
        """检测和识别人脸"""
        try:
            # 人脸检测
            faces = self.session.face_detection(frame)
            self.detected_faces = faces
            
            if faces:
                for face in faces:
                    # 绘制检测框
                    self.draw_face_box(frame, face)
                    
                    # 人脸识别
                    if self.feature_hub_enabled:
                        self.recognize_face(frame, face)
                        
        except Exception as e:
            print(f"人脸检测错误: {str(e)}")
    
    def draw_face_box(self, frame, face):
        """绘制人脸检测框"""
        x1, y1, x2, y2 = face.location
        
        # 计算旋转矩形
        center = ((x1 + x2) / 2, (y1 + y2) / 2)
        size = (x2 - x1, y2 - y1)
        angle = face.roll
        
        rect = ((center[0], center[1]), (size[0], size[1]), angle)
        box = cv2.boxPoints(rect).astype(int)
        
        # 绘制旋转的检测框
        cv2.drawContours(frame, [box], 0, (0, 255, 0), 2)
        
        # 绘制置信度
        confidence_text = f"{face.detection_confidence:.2f}"
        cv2.putText(frame, confidence_text, (int(x1), int(y1) - 10), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
    
    def recognize_face(self, frame, face):
        """识别人脸"""
        try:
            # 提取特征
            feature = self.session.face_feature_extract(frame, face)
            
            # 搜索相似人脸
            search_result = isf.feature_hub_face_search(feature)
            
            if search_result.similar_identity.id != -1 and search_result.confidence > 0.48:
                # 找到匹配的人脸
                face_id = search_result.similar_identity.id
                name = self.name_mapping.get(str(face_id), f"ID_{face_id}")
                confidence = search_result.confidence
                
                # 在人脸上显示姓名和置信度
                x1, y1, x2, y2 = face.location
                label = f"{name} ({confidence:.2f})"
                
                # 计算文本位置
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
                text_x = int(x1)
                text_y = int(y2) + 25
                
                # 绘制背景矩形
                cv2.rectangle(frame, (text_x, text_y - text_size[1] - 5), 
                            (text_x + text_size[0], text_y + 5), (0, 255, 0), -1)
                
                # 绘制文本
                cv2.putText(frame, label, (text_x, text_y), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
                
        except Exception as e:
            print(f"人脸识别错误: {e}")
    
    def register_face(self):
        """录入人脸"""
        if not self.detected_faces or self.current_frame is None:
            print("请先检测到人脸再进行录入")
            return
        
        # 生成随机姓名
        name = self.generate_random_name()
        print(f"正在录入人脸，姓名: {name}")
        
        try:
            # 使用第一个检测到的人脸
            face = self.detected_faces[0]
            
            # 提取特征
            feature = self.session.face_feature_extract(self.current_frame, face)
            
            # 创建人脸身份
            identity = isf.FaceIdentity(feature, id=-1)  # 使用自动分配ID
            
            # 插入到FeatureHub
            ret, alloc_id = isf.feature_hub_face_insert(identity)
            
            if ret:
                # 保存姓名映射
                self.name_mapping[str(alloc_id)] = name
                self.save_name_mapping()
                
                print(f"✓ 成功录入人脸: {name} (ID: {alloc_id})")
                
            else:
                print("✗ 人脸录入失败")
                
        except Exception as e:
            print(f"✗ 录入过程中发生错误: {str(e)}")
    
    def list_registered_faces(self):
        """列出已录入的人员"""
        print("\n已录入人员列表:")
        print("-" * 30)
        
        if not self.name_mapping:
            print("暂无已录入人员")
        else:
            for face_id, name in self.name_mapping.items():
                print(f"ID: {face_id} - 姓名: {name}")
        
        if self.feature_hub_enabled:
            total_count = isf.feature_hub_get_face_count()
            print(f"\n总计: {len(self.name_mapping)} 个已命名, {total_count} 个总数")
        
        print("-" * 30 + "\n")
    
    def show_status(self):
        """显示系统状态"""
        print("\n系统状态:")
        print("-" * 20)
        print(f"InspireFace状态: {'正常' if self.session else '异常'}")
        print(f"FeatureHub状态: {'启用' if self.feature_hub_enabled else '禁用'}")
        print(f"摄像头状态: {'正常' if self.cap.isOpened() else '异常'}")
        print(f"当前检测到人脸数: {len(self.detected_faces)}")
        
        if self.feature_hub_enabled:
            print(f"数据库中人脸总数: {isf.feature_hub_get_face_count()}")
        
        print("-" * 20 + "\n")

    def command_input_thread(self):
        """命令行输入线程"""
        print("命令行输入已启动，输入 'help' 查看帮助")

        while self.is_running:
            try:
                command = input(">>> ").strip().lower()
                if command:
                    self.command_queue.put(command)
            except (EOFError, KeyboardInterrupt):
                self.command_queue.put('quit')
                break
            except Exception as e:
                print(f"输入错误: {e}")

    def process_commands(self):
        """处理命令队列中的命令"""
        try:
            while not self.command_queue.empty():
                command = self.command_queue.get_nowait()

                if command == 'quit' or command == 'q':
                    self.is_running = False
                elif command == 'register' or command == 'r':
                    self.register_face()
                elif command == 'list' or command == 'l':
                    self.list_registered_faces()
                elif command == 'status' or command == 's':
                    self.show_status()
                elif command == 'help' or command == 'h':
                    self.print_help()
                else:
                    print(f"未知命令: {command}，输入 'help' 查看帮助")

        except queue.Empty:
            pass
        except Exception as e:
            print(f"命令处理错误: {e}")

    def print_help(self):
        """打印帮助信息"""
        print("\n可用命令:")
        print("- register (r): 录入当前检测到的人脸")
        print("- list (l): 列出所有已录入的人员")
        print("- status (s): 显示系统状态")
        print("- help (h): 显示此帮助信息")
        print("- quit (q): 退出程序")
        print("注意: 也可以在OpenCV窗口中按对应按键\n")

    def run(self):
        """运行应用程序"""
        print("开始人脸识别...")
        self.is_running = True

        # 启动命令行输入线程
        input_thread = threading.Thread(target=self.command_input_thread, daemon=True)
        input_thread.start()

        try:
            while self.is_running:
                ret, frame = self.cap.read()
                if not ret:
                    print("无法读取摄像头画面")
                    break

                self.current_frame = frame.copy()

                # 人脸检测和识别
                self.detect_and_recognize(frame)

                # 处理命令队列
                self.process_commands()

                # 显示画面
                cv2.imshow('InspireFace 人脸识别系统 (按q退出)', frame)

                # 处理OpenCV窗口按键（非阻塞）
                key = cv2.waitKey(1) & 0xFF

                if key == ord('q'):
                    self.is_running = False
                elif key == ord('r'):
                    self.register_face()
                elif key == ord('l'):
                    self.list_registered_faces()
                elif key == ord('s'):
                    self.show_status()
                elif key == ord('h'):
                    self.print_help()

        except KeyboardInterrupt:
            print("\n用户中断程序")

        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("正在清理资源...")
        
        self.is_running = False
        
        if hasattr(self, 'cap') and self.cap.isOpened():
            self.cap.release()
        
        cv2.destroyAllWindows()
        
        # 保存数据
        self.save_name_mapping()
        
        # 关闭InspireFace
        if self.session:
            self.session.release()
        
        if self.feature_hub_enabled:
            isf.feature_hub_disable()
        
        print("资源清理完成")


def main():
    """主函数"""
    try:
        app = FaceRecognitionCLI()
        app.run()
    except Exception as e:
        print(f"应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
