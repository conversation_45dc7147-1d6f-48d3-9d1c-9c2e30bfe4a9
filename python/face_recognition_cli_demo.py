#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
InspireFace 人脸识别命令行版本 - 演示版
演示真正的CLI交互界面，无需摄像头

功能特性：
- 真正的命令行交互
- 多线程架构演示
- 英文姓名支持
- 模拟人脸检测和识别
"""

import os
import json
import time
import threading
import queue
import random
from typing import Dict

class FaceRecognitionCLIDemo:
    def __init__(self):
        """初始化演示版人脸识别应用程序"""
        print("=" * 60)
        print("InspireFace 人脸识别系统 - CLI演示版")
        print("=" * 60)
        print("正在初始化...")
        
        # 应用状态
        self.is_running = False
        self.simulation_running = False
        self.detected_faces = []
        
        # 线程控制
        self.command_queue = queue.Queue()
        self.simulation_thread = None
        self.command_thread = None
        
        # 数据存储
        self.name_mapping_path = "name_mapping_demo.json"
        self.name_mapping = self.load_name_mapping()
        self.next_id = len(self.name_mapping) + 1
        
        print("✅ 系统初始化完成！")
        self.print_instructions()
        
    def load_name_mapping(self) -> Dict[int, str]:
        """加载姓名映射"""
        if os.path.exists(self.name_mapping_path):
            try:
                with open(self.name_mapping_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_name_mapping(self):
        """保存姓名映射"""
        try:
            with open(self.name_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self.name_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存姓名映射失败: {e}")
    
    def generate_random_name(self) -> str:
        """生成随机英文姓名"""
        first_names = ["James", "Mary", "John", "Patricia", "Robert", "Jennifer", "Michael", "Linda", 
                      "William", "Elizabeth", "David", "Barbara", "Richard", "Susan", "Joseph", "Jessica",
                      "Thomas", "Sarah", "Christopher", "Karen", "Charles", "Nancy", "Daniel", "Lisa"]
        
        last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
                     "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas"]
        
        first_name = random.choice(first_names)
        last_name = random.choice(last_names)
        return f"{first_name} {last_name}"
    
    def print_instructions(self):
        """打印操作说明"""
        print("\n" + "=" * 60)
        print("📖 操作说明:")
        print("1. 这是CLI交互界面的演示版本")
        print("2. 所有操作通过在此终端中输入命令完成")
        print("3. 可用命令:")
        print("   • register  → 模拟录入人脸")
        print("   • list      → 列出所有已录入的人员")
        print("   • status    → 显示系统状态")
        print("   • help      → 显示帮助信息")
        print("   • quit      → 退出程序")
        print("4. 在命令提示符后输入命令并按回车执行")
        print("5. 系统会模拟人脸检测和识别过程")
        print("=" * 60)
        print("💡 提示: 这是演示版，展示CLI交互方式！")
        print("=" * 60 + "\n")
    
    def simulation_thread_func(self):
        """模拟线程 - 模拟人脸检测过程"""
        print("🎭 模拟线程启动...")
        
        while self.simulation_running and self.is_running:
            try:
                # 模拟随机检测到人脸
                if random.random() < 0.3:  # 30%概率检测到人脸
                    num_faces = random.randint(1, 3)
                    self.detected_faces = [f"Face_{i}" for i in range(num_faces)]
                else:
                    self.detected_faces = []
                
                # 休眠模拟处理时间
                time.sleep(2)
                    
            except Exception as e:
                print(f"❌ 模拟处理错误: {e}")
                time.sleep(1)
        
        print("🎭 模拟线程结束")
    
    def command_input_thread_func(self):
        """命令输入处理线程"""
        print("⌨️  命令输入线程启动...")
        print("📝 请输入命令 (输入 'help' 查看可用命令):")
        
        while self.is_running:
            try:
                command = input(">>> ").strip().lower()
                if command:
                    self.command_queue.put(command)
                    
                    # 如果是退出命令，立即停止
                    if command in ['quit', 'exit', 'q']:
                        break
                        
            except (EOFError, KeyboardInterrupt):
                print("\n🔔 检测到中断信号，程序将退出...")
                self.command_queue.put('quit')
                break
            except Exception as e:
                print(f"❌ 输入错误: {e}")
        
        print("⌨️  命令输入线程结束")
    
    def process_commands(self):
        """处理命令队列中的命令"""
        try:
            while not self.command_queue.empty():
                command = self.command_queue.get_nowait()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 正在退出程序...")
                    self.is_running = False
                    self.simulation_running = False
                elif command in ['register', 'r']:
                    self.register_face()
                elif command in ['list', 'l']:
                    self.list_registered_faces()
                elif command in ['status', 's']:
                    self.show_status()
                elif command in ['help', 'h']:
                    self.show_help()
                elif command == '':
                    pass  # 忽略空命令
                else:
                    print(f"❓ 未知命令: '{command}'，输入 'help' 查看可用命令")
                    
        except queue.Empty:
            pass
        except Exception as e:
            print(f"❌ 命令处理错误: {e}")
    
    def register_face(self):
        """模拟录入人脸"""
        if not self.detected_faces:
            print("⚠️  当前未检测到人脸，请等待检测到人脸后再试")
            return
        
        # 生成随机英文姓名
        name = self.generate_random_name()
        print(f"🔄 正在录入人脸，姓名: {name}")
        
        try:
            # 模拟录入过程
            time.sleep(1)  # 模拟处理时间
            
            # 保存姓名映射
            self.name_mapping[str(self.next_id)] = name
            self.save_name_mapping()
            
            print(f"✅ 成功录入人脸: {name} (ID: {self.next_id})")
            self.next_id += 1
            
        except Exception as e:
            print(f"❌ 录入过程中发生错误: {str(e)}")
    
    def list_registered_faces(self):
        """列出已录入的人员"""
        print("\n" + "=" * 40)
        print("👥 已录入人员列表:")
        print("-" * 40)
        
        if not self.name_mapping:
            print("暂无已录入人员")
        else:
            for i, (face_id, name) in enumerate(self.name_mapping.items(), 1):
                print(f"{i:2d}. ID: {face_id:3s} - 姓名: {name}")
        
        print(f"\n📊 统计: {len(self.name_mapping)} 个已录入人员")
        print("=" * 40 + "\n")
    
    def show_status(self):
        """显示系统状态"""
        print("\n" + "=" * 40)
        print("📊 系统状态:")
        print("-" * 40)
        print(f"系统状态: {'✅ 运行中' if self.is_running else '❌ 已停止'}")
        print(f"模拟线程: {'✅ 运行中' if self.simulation_running else '❌ 已停止'}")
        print(f"当前检测到人脸数: {len(self.detected_faces)}")
        print(f"已录入人员总数: {len(self.name_mapping)}")
        print("=" * 40 + "\n")
    
    def show_help(self):
        """显示帮助信息"""
        print("\n" + "=" * 60)
        print("🆘 帮助信息:")
        print("-" * 60)
        print("可用命令 (在此终端中输入):")
        print("  register  - 模拟录入当前检测到的人脸")
        print("  list      - 列出所有已录入的人员")
        print("  status    - 显示系统状态")
        print("  help      - 显示此帮助信息")
        print("  quit      - 退出程序")
        print("\n简写命令:")
        print("  r         - register 的简写")
        print("  l         - list 的简写")
        print("  s         - status 的简写")
        print("  h         - help 的简写")
        print("  q         - quit 的简写")
        print("\n💡 使用技巧:")
        print("  • 这是演示版，展示真正的CLI交互方式")
        print("  • 系统会随机模拟检测到人脸")
        print("  • 所有操作都在此终端中完成")
        print("  • 系统会自动生成英文姓名")
        print("=" * 60 + "\n")
    
    def run(self):
        """运行应用程序 - 多线程架构演示"""
        print("🚀 启动人脸识别系统...")
        print("🎭 模拟模式：系统将随机模拟人脸检测")
        print("⌨️  所有操作请在此终端中输入命令")
        
        self.is_running = True
        self.simulation_running = True
        
        try:
            # 启动模拟线程
            self.simulation_thread = threading.Thread(target=self.simulation_thread_func, daemon=True)
            self.simulation_thread.start()
            
            # 等待模拟线程启动
            time.sleep(0.5)
            
            # 启动命令输入线程
            self.command_thread = threading.Thread(target=self.command_input_thread_func, daemon=True)
            self.command_thread.start()
            
            # 主线程负责处理命令队列
            print("✅ 系统启动完成！")
            print("💡 请在下方输入命令：")
            
            while self.is_running:
                # 处理命令队列
                self.process_commands()
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(0.1)
                
                # 检查线程状态
                if self.simulation_thread and not self.simulation_thread.is_alive():
                    print("🔔 模拟线程已停止")
                    self.is_running = False
                    break
                
                if self.command_thread and not self.command_thread.is_alive():
                    print("🔔 命令输入线程已停止")
                    self.is_running = False
                    break
                    
        except KeyboardInterrupt:
            print("\n👋 用户中断程序")
            self.is_running = False
            self.simulation_running = False
        
        finally:
            self.cleanup()
    
    def cleanup(self):
        """清理资源"""
        print("🔄 正在清理资源...")
        
        # 停止所有线程
        self.is_running = False
        self.simulation_running = False
        
        # 等待线程结束
        if hasattr(self, 'simulation_thread') and self.simulation_thread and self.simulation_thread.is_alive():
            print("⏳ 等待模拟线程结束...")
            self.simulation_thread.join(timeout=2)
        
        if hasattr(self, 'command_thread') and self.command_thread and self.command_thread.is_alive():
            print("⏳ 等待命令线程结束...")
            self.command_thread.join(timeout=2)
        
        # 保存数据
        self.save_name_mapping()
        
        print("✅ 资源清理完成")
        print("👋 感谢使用InspireFace人脸识别系统演示版！")


def main():
    """主函数"""
    try:
        app = FaceRecognitionCLIDemo()
        app.run()
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")


if __name__ == "__main__":
    main()
