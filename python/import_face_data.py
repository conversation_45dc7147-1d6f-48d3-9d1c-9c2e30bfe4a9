#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
从faceData.csv文件导入人脸数据到InspireFace数据库
下载图片，提取人脸特征，并保存到数据库中
"""

import os
import csv
import cv2
import json
import requests
import numpy as np
import inspireface as isf
from urllib.parse import urlparse
import time
from typing import Dict, List, Optional

class FaceDataImporter:
    def __init__(self, csv_file_path: str):
        """初始化人脸数据导入器"""
        self.csv_file_path = csv_file_path
        self.session = None
        self.feature_hub_enabled = False
        
        # 数据存储
        self.face_database_path = "face_database.db"
        self.name_mapping_path = "name_mapping.json"
        self.name_mapping = self.load_name_mapping()
        
        # 下载目录
        self.download_dir = "downloaded_faces"
        if not os.path.exists(self.download_dir):
            os.makedirs(self.download_dir)
        
        # 统计信息
        self.total_processed = 0
        self.successful_imports = 0
        self.failed_downloads = 0
        self.failed_detections = 0
        
        print("=" * 60)
        print("InspireFace 人脸数据导入工具")
        print("=" * 60)
        
    def setup_inspireface(self):
        """初始化InspireFace"""
        try:
            print("正在启动InspireFace...")
            
            # 启动InspireFace
            if not isf.query_launch_status():
                ret = isf.launch("Pikachu")
                if not ret:
                    raise Exception("InspireFace启动失败")
            
            # 创建会话
            opt = isf.HF_ENABLE_FACE_RECOGNITION
            self.session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)
            self.session.set_detection_confidence_threshold(0.5)
            
            # 配置FeatureHub
            feature_hub_config = isf.FeatureHubConfiguration(
                primary_key_mode=isf.HF_PK_AUTO_INCREMENT,
                enable_persistence=True,
                persistence_db_path=self.face_database_path,
                search_threshold=0.48,
                search_mode=isf.HF_SEARCH_MODE_EAGER,
            )
            
            ret = isf.feature_hub_enable(feature_hub_config)
            if not ret:
                raise Exception("FeatureHub启用失败")
            
            self.feature_hub_enabled = True
            print(f"✅ InspireFace初始化成功")
            
        except Exception as e:
            print(f"❌ InspireFace初始化失败: {e}")
            raise
    
    def load_name_mapping(self) -> Dict[str, str]:
        """加载姓名映射"""
        if os.path.exists(self.name_mapping_path):
            try:
                with open(self.name_mapping_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                return {}
        return {}
    
    def save_name_mapping(self):
        """保存姓名映射"""
        try:
            with open(self.name_mapping_path, 'w', encoding='utf-8') as f:
                json.dump(self.name_mapping, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存姓名映射失败: {e}")
    
    def download_image(self, url: str, filename: str) -> Optional[str]:
        """下载图片"""
        try:
            # 检查URL是否有效
            if not url or not url.startswith(('http://', 'https://')):
                print(f"❌ 无效的URL: {url}")
                return None
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            # 下载图片
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            # 保存图片
            file_path = os.path.join(self.download_dir, filename)
            with open(file_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 下载成功: {filename}")
            return file_path
            
        except Exception as e:
            print(f"❌ 下载失败 {url}: {e}")
            return None
    
    def extract_face_feature(self, image_path: str) -> Optional[np.ndarray]:
        """从图片中提取人脸特征"""
        try:
            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                print(f"❌ 无法读取图片: {image_path}")
                return None
            
            # 人脸检测
            faces = self.session.face_detection(image)
            if not faces:
                print(f"❌ 未检测到人脸: {image_path}")
                return None
            
            # 使用第一个检测到的人脸
            face = faces[0]
            print(f"✅ 检测到人脸，置信度: {face.detection_confidence:.2f}")
            
            # 提取特征
            feature = self.session.face_feature_extract(image, face)
            return feature
            
        except Exception as e:
            print(f"❌ 特征提取失败 {image_path}: {e}")
            return None
    
    def import_face_to_database(self, feature: np.ndarray, person_id: str) -> bool:
        """将人脸特征导入数据库"""
        try:
            # 创建人脸身份
            identity = isf.FaceIdentity(feature, id=-1)  # 使用自动分配ID
            
            # 插入到FeatureHub
            ret, alloc_id = isf.feature_hub_face_insert(identity)
            
            if ret:
                # 保存姓名映射
                self.name_mapping[str(alloc_id)] = person_id
                print(f"✅ 成功导入人脸: {person_id} (数据库ID: {alloc_id})")
                return True
            else:
                print(f"❌ 数据库插入失败: {person_id}")
                return False
                
        except Exception as e:
            print(f"❌ 导入数据库失败 {person_id}: {e}")
            return False
    
    def process_csv_file(self):
        """处理CSV文件"""
        try:
            print(f"正在读取CSV文件: {self.csv_file_path}")
            
            with open(self.csv_file_path, 'r', encoding='utf-8') as f:
                # 尝试检测CSV格式
                sample = f.read(1024)
                f.seek(0)
                
                # 检测分隔符
                delimiter = ','
                if '\t' in sample:
                    delimiter = '\t'
                elif ';' in sample:
                    delimiter = ';'
                
                reader = csv.DictReader(f, delimiter=delimiter)
                
                # 显示CSV列名
                print(f"CSV列名: {reader.fieldnames}")
                
                # 处理每一行
                for row_num, row in enumerate(reader, 1):
                    self.total_processed += 1
                    print(f"\n处理第 {row_num} 行...")
                    
                    # 尝试不同的列名组合
                    person_id = None
                    image_url = None
                    
                    # 查找ID列
                    for id_col in ['id', 'ID', 'person_id', 'name', 'label']:
                        if id_col in row and row[id_col]:
                            person_id = str(row[id_col]).strip()
                            break
                    
                    # 查找URL列
                    for url_col in ['url', 'URL', 'image_url', 'image', 'path', 'link']:
                        if url_col in row and row[url_col]:
                            image_url = str(row[url_col]).strip()
                            break
                    
                    if not person_id:
                        print(f"❌ 第 {row_num} 行缺少ID信息")
                        continue
                    
                    if not image_url:
                        print(f"❌ 第 {row_num} 行缺少图片URL")
                        continue
                    
                    print(f"处理人员: {person_id}, URL: {image_url}")
                    
                    # 检查是否已经存在
                    if person_id in self.name_mapping.values():
                        print(f"⚠️  人员 {person_id} 已存在，跳过")
                        continue
                    
                    # 下载图片
                    filename = f"{person_id}_{row_num}.jpg"
                    image_path = self.download_image(image_url, filename)
                    
                    if not image_path:
                        self.failed_downloads += 1
                        continue
                    
                    # 提取人脸特征
                    feature = self.extract_face_feature(image_path)
                    
                    if feature is None:
                        self.failed_detections += 1
                        continue
                    
                    # 导入数据库
                    if self.import_face_to_database(feature, person_id):
                        self.successful_imports += 1
                    
                    # 每10个保存一次映射
                    if self.successful_imports % 10 == 0:
                        self.save_name_mapping()
                    
                    # 短暂休眠避免请求过快
                    time.sleep(0.5)
                
        except Exception as e:
            print(f"❌ 处理CSV文件失败: {e}")
    
    def print_statistics(self):
        """打印统计信息"""
        print("\n" + "=" * 60)
        print("导入统计:")
        print("-" * 60)
        print(f"总处理数量: {self.total_processed}")
        print(f"成功导入: {self.successful_imports}")
        print(f"下载失败: {self.failed_downloads}")
        print(f"检测失败: {self.failed_detections}")
        print(f"成功率: {(self.successful_imports/self.total_processed*100):.1f}%" if self.total_processed > 0 else "0%")
        print("=" * 60)
    
    def run(self):
        """运行导入程序"""
        try:
            # 检查CSV文件
            if not os.path.exists(self.csv_file_path):
                print(f"❌ CSV文件不存在: {self.csv_file_path}")
                return
            
            # 初始化InspireFace
            self.setup_inspireface()
            
            # 处理CSV文件
            self.process_csv_file()
            
            # 保存最终映射
            self.save_name_mapping()
            
            # 打印统计信息
            self.print_statistics()
            
        except Exception as e:
            print(f"❌ 程序运行失败: {e}")
        
        finally:
            # 清理资源
            if self.session:
                self.session.release()
            if self.feature_hub_enabled:
                isf.feature_hub_disable()


def main():
    """主函数"""
    import sys
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        print("使用方法: python import_face_data.py <faceData.csv文件路径>")
        print("示例: python import_face_data.py faceData.csv")
        return
    
    csv_file_path = sys.argv[1]
    
    # 创建导入器并运行
    importer = FaceDataImporter(csv_file_path)
    importer.run()


if __name__ == "__main__":
    main()
