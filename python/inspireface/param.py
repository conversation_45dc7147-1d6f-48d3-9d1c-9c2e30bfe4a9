

# Session option
from inspireface.modules.core.native import HF_<PERSON>NABLE_NONE, HF_<PERSON><PERSON><PERSON>E_FACE_RECOGNITION, HF_<PERSON>NABLE_LIVENESS,  HF_ENABLE_IR_LIVENESS, \
    HF_ENABLE_MASK_DETECT, HF_<PERSON><PERSON>BLE_FACE_ATTRIBUTE, HF_<PERSON><PERSON>BLE_QUALITY, HF_ENABLE_INTERACTION, HF_ENABLE_FACE_POSE, HF_PK_AUTO_INCREMENT, HF_PK_MANUAL_INPUT, \
        HF_LANDMARK_HYPLMV2_0_25, HF_LANDMARK_HYPLMV2_0_50, HF_LANDMARK_INSIGHTFACE_2D106_TRACK

# Face track mode
from inspireface.modules.core.native import HF_DETECT_MODE_ALWAYS_DETECT, HF_DETECT_MODE_LIGHT_TRACK, HF_DETECT_MODE_TRACK_BY_DETECTION

# Image format
from inspireface.modules.core.native import HF_STREAM_RGB, HF_STREAM_BGR, HF_STREAM_RGBA, HF_STREAM_BGRA, HF_STREAM_YUV_NV12, HF_STREAM_YUV_NV21

# Image rotation
from inspireface.modules.core.native import HF_CAMERA_ROTATION_0, HF_CAMERA_ROTATION_90, HF_CAMERA_ROTATION_180, HF_CAMERA_ROTATION_270

# Search mode
from inspireface.modules.core.native import HF_SEARCH_MODE_EAGER, HF_SEARCH_MODE_EXHAUSTIVE

# Logger level
from inspireface.modules.core.native import HF_LOG_NONE, HF_LOG_DEBUG, HF_LOG_INFO, HF_LOG_WARN, HF_LOG_ERROR, HF_LOG_FATAL
