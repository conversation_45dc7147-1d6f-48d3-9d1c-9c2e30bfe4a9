#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速导入人脸数据脚本
支持多种CSV格式，自动检测列名
"""

import os
import csv
import cv2
import json
import requests
import inspireface as isf
import pandas as pd
from urllib.parse import urlparse

def setup_inspireface():
    """初始化InspireFace"""
    print("正在初始化InspireFace...")
    
    if not isf.query_launch_status():
        ret = isf.launch("Pikachu")
        if not ret:
            raise Exception("InspireFace启动失败")
    
    opt = isf.HF_ENABLE_FACE_RECOGNITION
    session = isf.InspireFaceSession(opt, isf.HF_DETECT_MODE_ALWAYS_DETECT)
    session.set_detection_confidence_threshold(0.5)
    
    feature_hub_config = isf.FeatureHubConfiguration(
        primary_key_mode=isf.HF_PK_AUTO_INCREMENT,
        enable_persistence=True,
        persistence_db_path="face_database.db",
        search_threshold=0.48,
        search_mode=isf.HF_SEARCH_MODE_EAGER,
    )
    
    ret = isf.feature_hub_enable(feature_hub_config)
    if not ret:
        raise Exception("FeatureHub启用失败")
    
    print("✅ InspireFace初始化成功")
    return session

def load_name_mapping():
    """加载现有的姓名映射"""
    if os.path.exists("name_mapping.json"):
        try:
            with open("name_mapping.json", 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    return {}

def save_name_mapping(mapping):
    """保存姓名映射"""
    with open("name_mapping.json", 'w', encoding='utf-8') as f:
        json.dump(mapping, f, ensure_ascii=False, indent=2)

def download_image(url, filename):
    """下载图片"""
    try:
        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        response = requests.get(url, headers=headers, timeout=30)
        response.raise_for_status()
        
        if not os.path.exists("temp_images"):
            os.makedirs("temp_images")
        
        file_path = os.path.join("temp_images", filename)
        with open(file_path, 'wb') as f:
            f.write(response.content)
        
        return file_path
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return None

def process_image(session, image_path, person_id, name_mapping):
    """处理单张图片"""
    try:
        # 读取图片
        image = cv2.imread(image_path)
        if image is None:
            print(f"❌ 无法读取图片: {image_path}")
            return False
        
        # 人脸检测
        faces = session.face_detection(image)
        if not faces:
            print(f"❌ 未检测到人脸: {person_id}")
            return False
        
        # 提取特征
        face = faces[0]
        feature = session.face_feature_extract(image, face)
        
        # 创建身份并插入数据库
        identity = isf.FaceIdentity(feature, id=-1)
        ret, alloc_id = isf.feature_hub_face_insert(identity)
        
        if ret:
            name_mapping[str(alloc_id)] = person_id
            print(f"✅ 成功导入: {person_id} (ID: {alloc_id})")
            return True
        else:
            print(f"❌ 数据库插入失败: {person_id}")
            return False
            
    except Exception as e:
        print(f"❌ 处理失败 {person_id}: {e}")
        return False

def analyze_csv(file_path):
    """分析CSV文件结构"""
    print(f"分析CSV文件: {file_path}")
    
    try:
        # 尝试用pandas读取
        df = pd.read_csv(file_path)
        print(f"CSV列名: {list(df.columns)}")
        print(f"总行数: {len(df)}")
        
        # 显示前几行
        print("\n前5行数据:")
        print(df.head())
        
        return df
        
    except Exception as e:
        print(f"❌ 读取CSV失败: {e}")
        return None

def auto_detect_columns(df):
    """自动检测ID和URL列"""
    columns = df.columns.tolist()
    
    # 检测ID列
    id_column = None
    for col in columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['id', 'name', 'label', 'person']):
            id_column = col
            break
    
    # 检测URL列
    url_column = None
    for col in columns:
        col_lower = col.lower()
        if any(keyword in col_lower for keyword in ['url', 'image', 'photo', 'pic', 'link']):
            url_column = col
            break
    
    print(f"检测到ID列: {id_column}")
    print(f"检测到URL列: {url_column}")
    
    return id_column, url_column

def main():
    """主函数"""
    import sys
    
    if len(sys.argv) != 2:
        print("使用方法: python quick_import.py <CSV文件路径>")
        print("示例: python quick_import.py faceData.csv")
        return
    
    csv_file = sys.argv[1]
    
    if not os.path.exists(csv_file):
        print(f"❌ 文件不存在: {csv_file}")
        return
    
    try:
        # 分析CSV文件
        df = analyze_csv(csv_file)
        if df is None:
            return
        
        # 自动检测列
        id_column, url_column = auto_detect_columns(df)
        
        if not id_column or not url_column:
            print("❌ 无法自动检测ID或URL列，请手动指定")
            print("可用列名:", list(df.columns))
            return
        
        # 用户确认
        print(f"\n将使用以下列:")
        print(f"ID列: {id_column}")
        print(f"URL列: {url_column}")
        
        confirm = input("是否继续? (y/n): ").lower().strip()
        if confirm not in ['y', 'yes', '是']:
            print("用户取消操作")
            return
        
        # 初始化InspireFace
        session = setup_inspireface()
        name_mapping = load_name_mapping()
        
        # 处理数据
        success_count = 0
        total_count = len(df)
        
        print(f"\n开始处理 {total_count} 条记录...")
        
        for index, row in df.iterrows():
            person_id = str(row[id_column]).strip()
            image_url = str(row[url_column]).strip()
            
            print(f"\n处理 {index+1}/{total_count}: {person_id}")
            
            # 检查是否已存在
            if person_id in name_mapping.values():
                print(f"⚠️  {person_id} 已存在，跳过")
                continue
            
            # 下载图片
            filename = f"{person_id}_{index}.jpg"
            image_path = download_image(image_url, filename)
            
            if image_path:
                # 处理图片
                if process_image(session, image_path, person_id, name_mapping):
                    success_count += 1
                
                # 删除临时文件
                try:
                    os.remove(image_path)
                except:
                    pass
            
            # 每10个保存一次
            if success_count % 10 == 0:
                save_name_mapping(name_mapping)
        
        # 保存最终结果
        save_name_mapping(name_mapping)
        
        # 打印统计
        print(f"\n" + "="*50)
        print(f"导入完成!")
        print(f"总处理: {total_count}")
        print(f"成功导入: {success_count}")
        print(f"成功率: {(success_count/total_count*100):.1f}%")
        print("="*50)
        
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
    
    finally:
        # 清理资源
        try:
            session.release()
            isf.feature_hub_disable()
        except:
            pass

if __name__ == "__main__":
    main()
