#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试英文姓名生成功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_name_generation():
    """测试姓名生成功能"""
    print("测试英文姓名生成功能...")
    
    try:
        # 导入简化版本的类
        from face_recognition_simple import SimpleFaceRecognition
        
        # 创建一个临时实例（不初始化摄像头和InspireFace）
        class NameTester:
            def generate_random_name(self):
                """生成随机英文姓名"""
                import random
                first_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", 
                              "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
                              "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
                
                last_names = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
                             "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]
                
                first_name = random.choice(first_names)
                last_name = random.choice(last_names)
                return f"{first_name} {last_name}"
        
        tester = NameTester()
        
        print("生成10个随机英文姓名:")
        print("-" * 30)
        
        for i in range(10):
            name = tester.generate_random_name()
            print(f"{i+1:2d}. {name}")
        
        print("-" * 30)
        print("✅ 英文姓名生成测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_camera_id():
    """测试摄像头ID设置"""
    print("\n测试摄像头ID设置...")
    
    try:
        import cv2
        
        # 测试摄像头ID 1
        cap = cv2.VideoCapture(1)
        if cap.isOpened():
            print("✅ 摄像头ID 1 可用")
            cap.release()
            return True
        else:
            print("⚠️  摄像头ID 1 不可用，尝试ID 0")
            cap.release()
            
            # 测试摄像头ID 0
            cap = cv2.VideoCapture(0)
            if cap.isOpened():
                print("✅ 摄像头ID 0 可用")
                cap.release()
                return True
            else:
                print("❌ 摄像头ID 0 和 1 都不可用")
                cap.release()
                return False
                
    except Exception as e:
        print(f"❌ 摄像头测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("英文姓名和摄像头ID测试")
    print("=" * 50)
    
    # 测试姓名生成
    name_test = test_name_generation()
    
    # 测试摄像头
    camera_test = test_camera_id()
    
    print("\n" + "=" * 50)
    print("测试结果:")
    print(f"英文姓名生成: {'✅ 通过' if name_test else '❌ 失败'}")
    print(f"摄像头ID设置: {'✅ 通过' if camera_test else '❌ 失败'}")
    
    if name_test and camera_test:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️  部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
