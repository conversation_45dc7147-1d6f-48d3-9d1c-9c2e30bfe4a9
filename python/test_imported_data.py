#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试导入的人脸数据
"""

import json
import os

def test_imported_data():
    """测试导入的数据"""
    print("=" * 50)
    print("测试导入的人脸数据")
    print("=" * 50)
    
    # 检查name_mapping.json文件
    if os.path.exists("name_mapping.json"):
        try:
            with open("name_mapping.json", 'r', encoding='utf-8') as f:
                name_mapping = json.load(f)
            
            print(f"✅ 姓名映射文件存在")
            print(f"📊 已导入人脸数量: {len(name_mapping)}")
            
            # 显示前10个导入的数据
            print("\n前10个导入的人脸:")
            print("-" * 30)
            count = 0
            for db_id, person_id in name_mapping.items():
                if count < 10:
                    print(f"数据库ID: {db_id} -> 人员ID: {person_id}")
                    count += 1
                else:
                    break
            
            if len(name_mapping) > 10:
                print(f"... 还有 {len(name_mapping) - 10} 个")
            
            # 显示最后10个导入的数据
            if len(name_mapping) > 10:
                print("\n最后10个导入的人脸:")
                print("-" * 30)
                items = list(name_mapping.items())
                for db_id, person_id in items[-10:]:
                    print(f"数据库ID: {db_id} -> 人员ID: {person_id}")
            
        except Exception as e:
            print(f"❌ 读取姓名映射文件失败: {e}")
    else:
        print("❌ 姓名映射文件不存在")
    
    # 检查数据库文件
    if os.path.exists("face_database.db"):
        file_size = os.path.getsize("face_database.db")
        print(f"\n✅ 人脸数据库文件存在")
        print(f"📁 数据库文件大小: {file_size / 1024 / 1024:.2f} MB")
    else:
        print("\n❌ 人脸数据库文件不存在")
    
    print("\n" + "=" * 50)

if __name__ == "__main__":
    test_imported_data()
