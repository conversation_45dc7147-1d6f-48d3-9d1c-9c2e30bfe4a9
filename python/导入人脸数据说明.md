# 人脸数据导入工具使用说明

## 概述

提供了两个脚本来从CSV文件导入人脸数据到InspireFace数据库：

1. **`import_face_data.py`** - 完整版本，功能全面
2. **`quick_import.py`** - 简化版本，快速导入

## 功能特性

- ✅ 自动下载网络图片
- ✅ 人脸检测和特征提取
- ✅ 批量导入到InspireFace数据库
- ✅ 自动检测CSV格式和列名
- ✅ 进度显示和错误处理
- ✅ 统计信息和成功率显示

## 使用方法

### 方法一：快速导入（推荐）

```bash
cd python
python quick_import.py faceData.csv
```

### 方法二：完整版本

```bash
cd python
python import_face_data.py faceData.csv
```

## CSV文件格式要求

### 支持的列名（自动检测）

**ID列（人员标识）：**
- `id`, `ID`
- `name`, `Name`
- `person_id`, `label`

**URL列（图片链接）：**
- `url`, `URL`
- `image_url`, `image`
- `photo`, `pic`, `link`

### 示例CSV格式

```csv
id,name,url
1,John Smith,https://example.com/face1.jpg
2,Jane Doe,https://example.com/face2.jpg
3,Bob Johnson,https://example.com/face3.jpg
```

或者：

```csv
person_id,image_url
Alice,https://images.com/alice.jpg
Bob,https://images.com/bob.jpg
Charlie,https://images.com/charlie.jpg
```

## 运行流程

### 1. 启动程序
```bash
python quick_import.py faceData.csv
```

### 2. 自动分析CSV
程序会自动：
- 读取CSV文件
- 检测列名和格式
- 显示前5行数据预览
- 自动识别ID列和URL列

### 3. 用户确认
```
检测到ID列: id
检测到URL列: url
是否继续? (y/n): y
```

### 4. 批量处理
对每条记录：
- 下载图片到临时目录
- 检测人脸
- 提取特征向量
- 保存到InspireFace数据库
- 更新姓名映射

### 5. 显示结果
```
导入完成!
总处理: 100
成功导入: 85
成功率: 85.0%
```

## 输出文件

### 1. `face_database.db`
- SQLite数据库文件
- 存储人脸特征向量
- InspireFace FeatureHub格式

### 2. `name_mapping.json`
- JSON格式的姓名映射文件
- 格式：`{"数据库ID": "人员姓名"}`
- 示例：
```json
{
  "1": "John Smith",
  "2": "Jane Doe",
  "3": "Bob Johnson"
}
```

### 3. `temp_images/` (临时目录)
- 下载的临时图片文件
- 处理完成后自动删除

## 错误处理

### 常见错误类型

1. **下载失败**
   - 网络连接问题
   - 图片URL无效
   - 服务器拒绝访问

2. **人脸检测失败**
   - 图片中没有人脸
   - 人脸太小或模糊
   - 图片质量差

3. **数据库错误**
   - InspireFace初始化失败
   - 特征提取失败
   - 数据库写入失败

### 错误示例输出
```
处理 1/100: John Smith
✅ 下载成功: John_Smith_1.jpg
✅ 检测到人脸，置信度: 0.95
✅ 成功导入: John Smith (ID: 1)

处理 2/100: Jane Doe
❌ 下载失败: 网络超时
```

## 性能优化

### 1. 网络设置
- 设置合适的超时时间（30秒）
- 使用User-Agent避免被拒绝
- 添加请求间隔避免过快

### 2. 批量处理
- 每10个成功导入保存一次映射
- 临时文件及时清理
- 内存使用优化

### 3. 错误恢复
- 跳过已存在的人员
- 继续处理后续数据
- 详细的错误日志

## 高级用法

### 1. 手动指定列名
如果自动检测失败，可以修改脚本中的列名：

```python
# 在auto_detect_columns函数中手动指定
id_column = "your_id_column"
url_column = "your_url_column"
```

### 2. 调整检测参数
```python
# 调整人脸检测置信度
session.set_detection_confidence_threshold(0.3)  # 降低阈值

# 调整识别阈值
search_threshold=0.40  # 在FeatureHubConfiguration中
```

### 3. 批量大小控制
```python
# 每N个保存一次
if success_count % 5 == 0:  # 改为5个保存一次
    save_name_mapping(name_mapping)
```

## 故障排除

### 1. InspireFace初始化失败
```bash
# 检查InspireFace安装
python -c "import inspireface; print('OK')"
```

### 2. 网络连接问题
- 检查网络连接
- 确认图片URL可访问
- 考虑使用代理

### 3. CSV格式问题
- 检查文件编码（建议UTF-8）
- 确认分隔符（逗号、制表符等）
- 验证列名是否正确

### 4. 内存不足
- 减少批量处理大小
- 及时清理临时文件
- 监控内存使用

## 注意事项

1. **数据备份**：导入前备份现有数据库
2. **网络流量**：大量图片下载会消耗较多流量
3. **处理时间**：根据图片数量和网络速度，可能需要较长时间
4. **图片质量**：确保图片中人脸清晰可见
5. **版权问题**：确保有权使用这些图片数据

## 示例命令

```bash
# 基本使用
python quick_import.py faceData.csv

# 检查现有数据
python -c "
import json
with open('name_mapping.json', 'r') as f:
    data = json.load(f)
    print(f'已有 {len(data)} 个人脸数据')
"

# 清理临时文件
rm -rf temp_images/
```

## 技术支持

如遇问题，请检查：
1. InspireFace是否正确安装
2. CSV文件格式是否正确
3. 网络连接是否正常
4. 图片URL是否有效

导入完成后，可以使用人脸识别CLI程序查看导入的数据：
```bash
python face_recognition_cli.py
```
